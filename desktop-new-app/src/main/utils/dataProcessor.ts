import { TransactionData } from '../../shared/api'

export function generateBankNoticeData(
  transactions: TransactionData[],
  metadata: Record<string, unknown> | null
): Record<string, unknown> {
  const banks: Record<string, TransactionData[]> = {}
  for (const transaction of transactions) {
    const bankName = (transaction.sender_bank ||
      transaction.receiver_bank ||
      'Unknown Bank') as string
    if (!banks[bankName]) {
      banks[bankName] = []
    }
    banks[bankName].push(transaction)
  }

  const totalAmount = transactions.reduce((sum, t) => {
    try {
      return sum + parseFloat(String(t.amount || 0).replace(/,/g, '') || '0')
    } catch {
      return sum
    }
  }, 0)

  return {
    banks,
    total_banks: Object.keys(banks).length,
    total_amount: totalAmount,
    metadata: metadata || {}
  }
}

export function generateGraphData(
  transactions: TransactionData[],
  metadata: Record<string, unknown> | null,
  maxLayer: number
): Record<string, unknown> {
  interface GraphNode {
    id: string
    label: string
    type: string
    layer: number
    bank: string
    fraud_type: string
  }

  interface GraphEdge {
    id: string
    source: string
    target: string
    amount: string
    date: string
    type: string
    layer: number
    transaction_id: string
    reference: string
    fraud_type: string
  }

  const nodes: GraphNode[] = []
  const edges: GraphEdge[] = []
  const accountNodes = new Set<string>()

  for (const transaction of transactions) {
    const senderAccount = transaction.sender_account
    const receiverAccount = transaction.receiver_account
    const layerIntForGraph = transaction.layer || 0

    if (senderAccount && !accountNodes.has(senderAccount)) {
      nodes.push({
        id: senderAccount,
        label: senderAccount.length > 20 ? senderAccount.substring(0, 20) + '...' : senderAccount,
        type: 'sender_account',
        layer: layerIntForGraph,
        bank: transaction.sender_bank || '',
        fraud_type: transaction.fraud_type || ''
      })
      accountNodes.add(senderAccount)
    }

    if (receiverAccount && !accountNodes.has(receiverAccount)) {
      nodes.push({
        id: receiverAccount,
        label:
          receiverAccount.length > 20 ? receiverAccount.substring(0, 20) + '...' : receiverAccount,
        type: 'receiver_account',
        layer: layerIntForGraph,
        bank: transaction.receiver_bank || '',
        fraud_type: transaction.fraud_type || ''
      })
      accountNodes.add(receiverAccount)
    }

    if (senderAccount && receiverAccount) {
      const edgeId = `edge_${nodes.length}_${senderAccount}_${receiverAccount}`
      edges.push({
        id: edgeId,
        source: senderAccount,
        target: receiverAccount,
        amount: transaction.amount || '0',
        date: transaction.date || '',
        type: 'transfer',
        layer: layerIntForGraph,
        transaction_id: transaction.sender_transaction_id || '',
        reference: transaction.receiver_info || '',
        fraud_type: transaction.fraud_type || ''
      })
    }
  }

  let totalAmount = 0
  let suspiciousNodes = 0
  let suspiciousEdges = 0

  for (const transaction of transactions) {
    try {
      const amount = parseFloat(String(transaction.amount || 0).replace(/,/g, '') || '0')
      totalAmount += amount
      if (amount > 50000) {
        suspiciousEdges += 1
      }
    } catch (e) {
      console.error('Error parsing transaction amount for graph stats:', e)
    }
  }

  const accountAmounts: Record<string, number> = {}
  for (const transaction of transactions) {
    const sender = transaction.sender_account
    if (sender) {
      try {
        const amount = parseFloat(String(transaction.amount || 0).replace(/,/g, '') || '0')
        accountAmounts[sender] = (accountAmounts[sender] || 0) + amount
      } catch (e) {
        console.error('Error parsing account amount for graph stats:', e)
      }
    }
  }

  for (const account in accountAmounts) {
    if (accountAmounts[account] > 100000) {
      suspiciousNodes += 1
    }
  }

  return {
    nodes,
    edges,
    transactions,
    metadata: {
      total_nodes: nodes.length,
      total_edges: edges.length,
      max_layer: maxLayer,
      total_amount: totalAmount,
      suspicious_nodes: suspiciousNodes,
      suspicious_edges: suspiciousEdges,
      complaint_metadata: metadata || {}
    },
    max_layer: maxLayer
  }
}
