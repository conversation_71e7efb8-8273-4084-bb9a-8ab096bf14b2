import { useState, useEffect, useCallback } from 'react'
import { backendService } from '../services/backendService'
import { ComplaintData } from '../../../shared/api'

export const useComplaintData = (
  complaintId: string
): {
  data: ComplaintData | null
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
} => {
  const [data, setData] = useState<ComplaintData | null>(null)
  const [loading, setLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)

  const fetchComplaint = useCallback(async () => {
    if (!complaintId) {
      setError('Complaint ID is missing.')
      setLoading(false)
      return
    }

    setLoading(true)
    setError(null)

    try {
      const complaint = await backendService.getLocalComplaint(complaintId)
      if (complaint) {
        setData(complaint)
      } else {
        setError('Complaint not found.')
      }
    } catch (err) {
      console.error('Error fetching complaint data:', err)
      setError('Failed to load complaint data.')
    } finally {
      setLoading(false)
    }
  }, [complaintId])

  useEffect(() => {
    fetchComplaint()
  }, [fetchComplaint])

  return { data, loading, error, refetch: fetchComplaint }
}
