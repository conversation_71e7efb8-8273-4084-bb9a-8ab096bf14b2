import { useState, useEffect, useCallback } from 'react'
import { backendService } from '../services/backendService'
import { ComplaintData, ComplaintGraphData } from '../../../shared/api'

export const useComplaintData = (
  complaintId: string
): {
  data: ComplaintData | null
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
} => {
  const [data, setData] = useState<ComplaintData | null>(null)
  const [loading, setLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)

  const fetchComplaint = useCallback(async () => {
    if (!complaintId) {
      setError('Complaint ID is missing.')
      setLoading(false)
      return
    }

    setLoading(true)
    setError(null)

    try {
      const complaint = await backendService.getLocalComplaint(complaintId)
      if (complaint) {
        setData(complaint)
      } else {
        setError('Complaint not found.')
      }
    } catch (err) {
      console.error('Error fetching complaint data:', err)
      setError('Failed to load complaint data.')
    } finally {
      setLoading(false)
    }
  }, [complaintId])

  useEffect(() => {
    fetchComplaint()
  }, [fetchComplaint])

  return { data, loading, error, refetch: fetchComplaint }
}

/**
 * Custom hook for fetching and managing graph data specifically
 * @param complaintId The ID of the complaint to fetch graph data for
 * @returns Object containing loading state, error state, graph data, and refetch function
 */
export const useGraphData = (
  complaintId: string
): {
  graphData: ComplaintGraphData | null
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
} => {
  const [graphData, setGraphData] = useState<ComplaintGraphData | null>(null)
  const [loading, setLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)

  const fetchGraphData = useCallback(async () => {
    if (!complaintId) {
      setError('Complaint ID is missing.')
      setLoading(false)
      return
    }

    setLoading(true)
    setError(null)

    try {
      const complaint = await backendService.getLocalComplaint(complaintId)
      if (complaint) {
        // Extract graph data from complaint - use the pre-processed graph_data
        if (complaint.graph_data) {
          setGraphData(complaint.graph_data)
        } else {
          setError('No graph data available for this complaint.')
        }
      } else {
        setError('Complaint not found.')
      }
    } catch (err) {
      console.error('Error fetching graph data:', err)
      setError('Failed to load graph data.')
    } finally {
      setLoading(false)
    }
  }, [complaintId])

  useEffect(() => {
    fetchGraphData()
  }, [fetchGraphData])

  return { graphData, loading, error, refetch: fetchGraphData }
}
