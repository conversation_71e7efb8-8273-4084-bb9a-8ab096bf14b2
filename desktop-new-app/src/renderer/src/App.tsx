import React, { useContext } from 'react'
import { AuthContext } from './context/AuthContext'
import { Routes, Route, Navigate, useNavigate, useLocation } from 'react-router-dom'
import Login from './pages/Login'
import Signup from './pages/Signup'
import VerifyOTP from './pages/VerifyOTP'
import Dashboard from './pages/Dashboard'
import ComplaintDetails from './pages/ComplaintDetails'
import ComplaintSummary from './pages/ComplaintSummary'
import ComplaintUpload from './pages/ComplaintUpload'
import Settings from './pages/Settings'
import Profile from './pages/Profile'
import GraphVisualization from './pages/GraphVisualization'
import InformationSearchPage from './pages/InformationSearchPage'
import CommonAccountAnalysis from './pages/CommonAccountAnalysis'
import NoticeGenerationPage from './pages/NoticeGenerationPage'
import { Sidebar, SidebarBody, SidebarLink } from './components/ui/sidebar'
import { IconArrowLeft, IconBrandTabler, IconSettings, IconUserBolt } from '@tabler/icons-react'
import { Link } from 'react-router-dom'
import { AuthProvider } from './context/AuthContext.tsx'
import { ThemeProvider } from './context/ThemeContext.tsx'
import GradientBackground from './components/GradientBackground.tsx'

const App: React.FC = () => {
  return (
    <AuthProvider>
      <ThemeProvider defaultTheme="dark" storageKey="vite-ui-theme">
        <AppContent />
      </ThemeProvider>
    </AuthProvider>
  )
}

const AppContent: React.FC = () => {
  const { isAuthenticated, isLoading } = useContext(AuthContext) || {}
  const navigate = useNavigate()
  const location = useLocation()

  const handleLoginSuccess = (): void => {
    navigate('/dashboard')
  }

  const links = [
    {
      label: 'Dashboard',
      href: '/dashboard',
      icon: <IconBrandTabler className="text-foreground h-5 w-5 flex-shrink-0" />
    },
    {
      label: 'Upload Complaint',
      href: '/upload',
      icon: <IconUserBolt className="text-foreground h-5 w-5 flex-shrink-0" />
    },
    {
      label: 'Analysis',
      href: '/stats',
      icon: <IconUserBolt className="text-foreground h-5 w-5 flex-shrink-0" />
    },
    {
      label: 'Information Search',
      href: '/information-search',
      icon: <IconUserBolt className="text-foreground h-5 w-5 flex-shrink-0" />
    },
    {
      label: 'Profile',
      href: '/profile',
      icon: <IconUserBolt className="text-foreground h-5 w-5 flex-shrink-0" />
    },
    {
      label: 'Settings',
      href: '/settings',
      icon: <IconSettings className="text-foreground h-5 w-5 flex-shrink-0" />
    },
    {
      label: 'Logout',
      href: '/login',
      icon: <IconArrowLeft className="text-foreground h-5 w-5 flex-shrink-0" />
    }
  ]

  // Show nothing until auth check completes
  if (isLoading) {
    return null
  }

  const isAuthPage = ['/login', '/signup', '/verify-otp'].includes(location.pathname)

  return (
    <div className="min-h-screen flex">
      {isAuthenticated && (
        <Sidebar>
          <SidebarBody className="justify-between gap-10">
            <div className="flex flex-col flex-1 overflow-y-auto">
              <div className="mt-8">
                <div className="flex flex-col gap-2">
                  {links.map((link, idx) => (
                    <Link to={link.href} key={idx}>
                      <SidebarLink link={link} />
                    </Link>
                  ))}
                </div>
              </div>
            </div>
          </SidebarBody>
        </Sidebar>
      )}
      {isAuthPage ? (
        <main className="flex-grow">
          <Routes>
            <Route
              path="/login"
              element={
                isAuthenticated ? (
                  <Navigate to="/dashboard" replace />
                ) : (
                  <Login onLoginSuccess={handleLoginSuccess} />
                )
              }
            />
            <Route path="/signup" element={<Signup />} />
            <Route path="/verify-otp" element={<VerifyOTP />} />
            <Route
              path="/dashboard"
              element={isAuthenticated ? <Dashboard /> : <Navigate to="/login" replace />}
            />
            <Route
              path="/complaints"
              element={isAuthenticated ? <ComplaintSummary /> : <Navigate to="/login" replace />}
            />
            <Route
              path="/complaint-details/:id"
              element={isAuthenticated ? <ComplaintDetails /> : <Navigate to="/login" replace />}
            />
            <Route
              path="/upload"
              element={isAuthenticated ? <ComplaintUpload /> : <Navigate to="/login" replace />}
            />
            <Route
              path="/stats"
              element={isAuthenticated ? <GraphVisualization /> : <Navigate to="/login" replace />}
            />
            <Route
              path="/profile"
              element={isAuthenticated ? <Profile /> : <Navigate to="/login" replace />}
            />
            <Route
              path="/settings"
              element={isAuthenticated ? <Settings /> : <Navigate to="/login" replace />}
            />
            <Route
              path="/information-search"
              element={
                isAuthenticated ? <InformationSearchPage /> : <Navigate to="/login" replace />
              }
            />
            <Route
              path="/common-account-analysis"
              element={
                isAuthenticated ? <CommonAccountAnalysis /> : <Navigate to="/login" replace />
              }
            />
            <Route
              path="/notice-generation"
              element={
                isAuthenticated ? <NoticeGenerationPage /> : <Navigate to="/login" replace />
              }
            />
            <Route path="*" element={<Navigate to="/login" replace />} />
          </Routes>
        </main>
      ) : (
        <GradientBackground>
          <main className="flex-grow">
            <Routes>
              <Route
                path="/login"
                element={
                  isAuthenticated ? (
                    <Navigate to="/dashboard" replace />
                  ) : (
                    <Login onLoginSuccess={handleLoginSuccess} />
                  )
                }
              />
              <Route path="/signup" element={<Signup />} />
              <Route path="/verify-otp" element={<VerifyOTP />} />
              <Route
                path="/dashboard"
                element={isAuthenticated ? <Dashboard /> : <Navigate to="/login" replace />}
              />
              <Route
                path="/complaints"
                element={isAuthenticated ? <ComplaintSummary /> : <Navigate to="/login" replace />}
              />
              <Route
                path="/complaint-details/:id"
                element={isAuthenticated ? <ComplaintDetails /> : <Navigate to="/login" replace />}
              />
              <Route
                path="/upload"
                element={isAuthenticated ? <ComplaintUpload /> : <Navigate to="/login" replace />}
              />
              <Route
                path="/stats"
                element={
                  isAuthenticated ? <GraphVisualization /> : <Navigate to="/login" replace />
                }
              />
              <Route
                path="/profile"
                element={isAuthenticated ? <Profile /> : <Navigate to="/login" replace />}
              />
              <Route
                path="/settings"
                element={isAuthenticated ? <Settings /> : <Navigate to="/login" replace />}
              />
              <Route
                path="/information-search"
                element={
                  isAuthenticated ? <InformationSearchPage /> : <Navigate to="/login" replace />
                }
              />
              <Route
                path="/common-account-analysis"
                element={
                  isAuthenticated ? <CommonAccountAnalysis /> : <Navigate to="/login" replace />
                }
              />
              <Route
                path="/notice-generation"
                element={
                  isAuthenticated ? <NoticeGenerationPage /> : <Navigate to="/login" replace />
                }
              />
              <Route path="*" element={<Navigate to="/login" replace />} />
            </Routes>
          </main>
        </GradientBackground>
      )}
    </div>
  )
}

export default App
