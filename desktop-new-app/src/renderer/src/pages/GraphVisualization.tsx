import React, { useState } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { ArrowLeft, FileText } from 'lucide-react'
import { backendService } from '../services/backendService'
import { ComplaintData, TransactionData } from '../../../shared/api'
import { useGraphData } from '../hooks/useGraphData'
import { GraphContainer } from '../components/graph/GraphContainer'
import { useAlert } from '../context/AlertContext'
import { useTheme } from '../context/ThemeContext'

const GraphVisualization: React.FC = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const { isDark } = useTheme()
  const { showError } = useAlert()
  const { graphData, loading, error } = useGraphData(id as string)
  const [isExporting, setIsExporting] = useState(false)
  const [exportProgress, setExportProgress] = useState<{ current: number; total: number; currentPage?: string }>({
    current: 0,
    total: 100
  })

  const handleExportProgressChange = (progress: { current: number; total: number; currentPage?: string }) => {
    setExportProgress(progress)
  }

  const handleExportStateChange = (isExporting: boolean) => {
    setIsExporting(isExporting)
  }

  if (error) {
    showError(error)
    navigate('/dashboard')
  }

  return (
    <div className="h-screen flex flex-col">
      <div className="flex items-center justify-between p-4 border-b">
        <button
          onClick={() => navigate('/dashboard')}
          className="flex items-center gap-2 px-3 py-2 rounded bg-neutral-800 hover:bg-neutral-700 transition-colors"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Dashboard
        </button>
        <h1 className="text-xl font-semibold text-white">Graph Analysis - Complaint {id}</h1>
      </div>
      <div className="flex-1 relative">
        {graphData ? (
          <GraphContainer
            complaintData={graphData}
            complaintId={id as string}
            onExportStateChange={handleExportStateChange}
            onExportProgressChange={handleExportProgressChange}
          />
        ) : (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <p className="text-lg text-white">No graph data available</p>
              <button
                onClick={() => navigate('/dashboard')}
                className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
              >
                Back to Dashboard
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default GraphVisualization
