import React, { useState, useCallback, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { CardContainer, CardBody } from '../components/ui/3d-card'
import { ArrowLeft, BarChart3, FileText, Download } from 'lucide-react'
import { backendService } from '../services/backendService'
import { ComplaintData, TransactionData } from '../../../shared/api'
import CsvDataTable, { RowOperationMetadata, CSVRow } from '../components/CsvDataTable'

const ComplaintDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()

  const [complaint, setComplaint] = useState<ComplaintData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isDownloading, setIsDownloading] = useState(false) // Define loadComplaint outside useEffect to be callable
  const loadComplaint = useCallback(async (): Promise<void> => {
    if (!id) return

    try {
      setLoading(true)
      const complaintData = await backendService.getLocalComplaint(id)
      if (complaintData) {
        setComplaint(complaintData)
      } else {
        setError('Complaint not found')
      }
    } catch (err) {
      setError('Failed to load complaint data')
      console.error('Error loading complaint:', err)
    } finally {
      setLoading(false)
    }
  }, [id])

  // Load complaint data on component mount and id change
  useEffect(() => {
    loadComplaint()
  }, [loadComplaint])

  // Handle CSV download
  const handleDownloadCSV = useCallback(async () => {
    if (!complaint || !complaint.layer_transactions) return

    const allTransactions = Object.values(
      complaint.layer_transactions
    ).flat() as import('../../../shared/api').TransactionData[]

    if (allTransactions.length === 0) return

    setIsDownloading(true)
    try {
      // Generate CSV from transactions
      const headers = [
        'Layer',
        'Fraud Type',
        'Sender Account',
        'Sender Txn ID',
        'Sender Bank',
        'Receiver Account',
        'Txn ID',
        'Receiver Bank',
        'Txn Type',
        'Txn Date',
        'Amount',
        'Reference'
      ]

      let csvContent = headers.join(',') + '\n'

      allTransactions.forEach((txn: import('../../../shared/api').TransactionData) => {
        const row = [
          txn.layer || '',
          txn.fraud_type || 'banking_upi',
          txn.sender_account || '',
          txn.sender_transaction_id || '',
          txn.sender_bank || '',
          txn.receiver_account || '',
          txn.receiver_transaction_id || '',
          txn.receiver_bank || '',
          txn.type || txn.txn_type || '',
          txn.date || '',
          txn.amount || '',
          txn.reference || txn.receiver_info || ''
        ]

        const escapedRow = row.map((field) => {
          if (field && field.toString().includes(',')) {
            return `"${field}"`
          }
          return field
        })

        csvContent += escapedRow.join(',') + '\n'
      })

      // Create and download file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', `complaint_${id}_transactions.csv`)
      link.style.visibility = 'hidden'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    } catch (error) {
      console.error('Failed to download CSV:', error)
    } finally {
      setIsDownloading(false)
    }
  }, [complaint, id])

  // Handle saving CSV data from CsvDataTable
  const handleCsvSave = useCallback(
    async (updatedCsvData: string, metadata?: RowOperationMetadata) => {
      if (!id || !complaint || !metadata) return // Ensure metadata is not undefined

      setLoading(true) // Set loading true for the save operation

      try {
        // The metadata.updatedRows contains the full updated array of rows
        // We need to convert this back to the structure expected by backendService.updateComplaint
        // which is layer_transactions: Record<string, unknown>
        const updatedLayerTransactions: Record<string, TransactionData[]> = {}
        metadata.updatedRows.forEach((csvRow: CSVRow) => {
          const transactionData: TransactionData = {
            fraud_type: csvRow.fraud_type as string,
            sender_account: csvRow.sender_account as string,
            sender_transaction_id: csvRow.sender_transaction_id as string,
            sender_bank: csvRow.sender_bank as string,
            layer: csvRow.layer as number,
            txn_type: csvRow.txn_type as string,
            type: csvRow.type as string,
            date: csvRow.date as string,
            receiver_bank: csvRow.receiver_bank as string,
            receiver_account: csvRow.receiver_account as string,
            receiver_transaction_id: csvRow.receiver_transaction_id as string,
            amount: csvRow.amount as string,
            receiver_ifsc: csvRow.receiver_ifsc as string,
            receiver_info: csvRow.receiver_info as string,
            reference: csvRow.reference as string,
            sr_no: csvRow.sr_no as string,
            extracted_at: csvRow.extracted_at as string,
            source: csvRow.source as string,
            is_valid: csvRow.is_valid as string,
            validation_errors: csvRow.validation_errors as string
          }

          const layer = transactionData.layer || 'default'
          if (!updatedLayerTransactions[layer]) {
            updatedLayerTransactions[layer] = []
          }
          updatedLayerTransactions[layer].push(transactionData)
        })

        // Re-generate graph_data and bank_notice_data
        const allTransactions = Object.values(updatedLayerTransactions).flat()
        const newGraphData = await backendService.generateGraphData(
          allTransactions,
          complaint.metadata || null,
          complaint.max_layer || 7
        )
        const newBankNoticeData = await backendService.generateBankNoticeData(
          allTransactions,
          complaint.metadata || null
        )

        const success = await backendService.updateComplaint(id, {
          layer_transactions: updatedLayerTransactions,
          graph_data: newGraphData,
          bank_notice_data: newBankNoticeData
        })

        if (success) {
          // Re-fetch the complaint to get the latest state from the database
          await loadComplaint()
        } else {
          setError('Failed to save transaction data.')
        }
      } catch (err) {
        setError('Error saving transaction data.')
        console.error('Error saving transaction:', err)
      } finally {
        setLoading(false) // Set loading false after the operation
      }
    },
    [id, loadComplaint, complaint]
  ) // Added 'complaint' back to dependencies as it's used for metadata and max_layer

  // Navigate to graph visualization
  const handleGraphVisualization = (): void => {
    navigate(`/graph/${id}`)
  }

  // Navigate to notice generation
  const handleNoticeGeneration = (): void => {
    navigate(`/notices/${id}`)
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-full w-full">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6 w-full">
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800/50 rounded-lg p-4 text-red-800 dark:text-red-300">
          <p>{error}</p>
        </div>
      </div>
    )
  }

  if (!complaint) {
    return (
      <div className="p-6 w-full">
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800/50 rounded-lg p-4 text-yellow-800 dark:text-yellow-300">
          <p>No complaint data available</p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex min-h-screen bg-neutral-900 text-white overflow-x-hidden">
      <main className="flex-1 p-6 sm:p-10 overflow-y-auto relative min-w-0 w-full">
        {' '}
        {/* Added w-full here */}
        {/* Header with complaint info and navigation */}
        <div className="sticky top-0 bg-neutral-900 z-10 pb-4 flex flex-col md:flex-row justify-between items-start md:items-center gap-2 w-full p-4">
          <div className="flex-shrink-0">
            <div className="flex items-center gap-2 mb-1">
              <button
                onClick={() => navigate('/dashboard')}
                className="flex items-center gap-2 px-4 py-2 rounded-lg bg-neutral-800 hover:bg-neutral-700 transition-colors"
              >
                <ArrowLeft className="h-4 w-4" />
                Back to Dashboard
              </button>
            </div>
            <h1 className="text-xl font-semibold text-white">{complaint.title}</h1>
            <p className="text-sm text-gray-400">
              Created: {new Date(complaint.created_at).toLocaleDateString()}
            </p>
          </div>

          <div className="flex flex-wrap gap-2 justify-end">
            <button
              onClick={handleGraphVisualization}
              className="flex items-center gap-2 px-4 py-2 rounded-lg bg-primary-500 hover:bg-primary-600 transition-colors"
            >
              <BarChart3 className="h-4 w-4" />
              Graph Analysis
            </button>
            <button
              onClick={handleNoticeGeneration}
              className="flex items-center gap-2 px-4 py-2 rounded-lg bg-secondary-500 hover:bg-secondary-600 transition-colors"
            >
              <FileText className="h-4 w-4" />
              Generate Notices
            </button>
            <button
              onClick={handleDownloadCSV}
              disabled={isDownloading || !complaint.transactions?.length}
              className="flex items-center gap-2 px-4 py-2 rounded-lg bg-neutral-800 hover:bg-neutral-700 transition-colors disabled:opacity-50"
            >
              <Download className="h-4 w-4" />
              {isDownloading ? 'Downloading...' : 'Download CSV'}
            </button>
          </div>
        </div>
        {/* Transaction Table */}
        <div className="flex-grow w-full max-w-full overflow-x-auto px-4">
          {complaint.layer_transactions &&
          Object.values(complaint.layer_transactions).flat().length > 0 ? (
            <CsvDataTable
              data={Object.values(complaint.layer_transactions)
                .flat()
                .filter(
                  (item): item is import('../../../shared/api').TransactionData =>
                    typeof item === 'object' &&
                    item !== null &&
                    'txn_type' in item &&
                    'amount' in item
                )}
              loading={loading}
              onSave={handleCsvSave}
              hiddenColumns={[
                'type',
                'fraud_type',
                'sender_transaction_id',
                'reference',
                'extracted_at',
                'source',
                'is_valid',
                'validation_errors',
                'suspect_metadata'
              ]}
            />
          ) : (
            <CardContainer>
              <CardBody>
                <div className="p-8 text-center">
                  <p className="text-gray-400">No transaction data available</p>
                </div>
              </CardBody>
            </CardContainer>
          )}
        </div>
        {/* Removed BackgroundBeams */}
        <div className="absolute inset-0 -z-10 bg-gradient-to-b from-neutral-900 to-black"></div>
      </main>
    </div>
  )
}

export default ComplaintDetails
