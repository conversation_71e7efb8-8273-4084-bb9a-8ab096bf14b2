import React, { useEffect, useCallback, useState } from 'react'
import {
  ReactFlow,
  Background,
  Controls,
  MiniMap,
  useNodesState,
  useEdgesState,
  addEdge,
  Connection,
  Edge,
  Node,
  Panel
} from '@xyflow/react'
import '@xyflow/react/dist/style.css'

// Import node components
import { TransactionNode } from './nodes/TransactionNode'
import { MetadataNode } from './nodes/MetadataNode'
import { useThemeContext } from '../../context/useThemeContext'
import { useAlert } from '../../hooks/useAlert'
import { ComplaintGraphData } from '../../../../shared/api'

// Define node types using ReactFlow's standard approach
const nodeTypes = {
  transaction: TransactionNode,
  metadata: MetadataNode
}

interface GraphContainerProps {
  complaintData: ComplaintGraphData
  complaintId: string
  onExportStateChange?: (isExporting: boolean) => void
  onExportProgressChange?: (progress: { current: number; total: number; currentPage?: string }) => void
}

export const GraphContainer: React.FC<GraphContainerProps> = ({
  complaintData,
  complaintId,
  onExportStateChange,
  onExportProgressChange
}) => {
  const { isDark } = useThemeContext()
  const { showError } = useAlert()

  const [nodes, setNodes, onNodesChange] = useNodesState([])
  const [edges, setEdges, onEdgesChange] = useEdgesState([])

  // Simple state for tracking node visibility
  const [isGloballyCollapsed, setIsGloballyCollapsed] = useState(false)

  // Handle new connections between nodes
  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  )

  // Process graph data when complaint data changes
  useEffect(() => {
    if (!complaintData) return

    try {
      // Create sample nodes and edges from the complaint data
      const initialNodes: Node[] = []
      const initialEdges: Edge[] = []

      // Create metadata node
      if (complaintData.metadata) {
        initialNodes.push({
          id: 'metadata',
          type: 'metadata',
          position: { x: 250, y: 50 },
          data: {
            label: 'Complaint Metadata',
            ...complaintData.metadata
          }
        })
      }

      // Create transaction nodes from the transactions data
      if (complaintData.transactions && Array.isArray(complaintData.transactions)) {
        complaintData.transactions.forEach((transaction, index) => {
          const nodeId = `transaction-${index}`

          initialNodes.push({
            id: nodeId,
            type: 'transaction',
            position: {
              x: 100 + (index % 3) * 300,
              y: 200 + Math.floor(index / 3) * 200
            },
            data: {
              label: `Transaction ${index + 1}`,
              account: transaction.receiver_account || transaction.sender_account,
              amount: transaction.amount,
              date: transaction.date,
              type: transaction.type,
              layer: transaction.layer,
              txn_id: transaction.txn_id || transaction.transaction_id,
              sender_account: transaction.sender_account,
              receiver_info: transaction.receiver_info,
              isSenderNode: transaction.layer === 1,
              isMainTransaction: transaction.type === 'Money Transfer to'
            }
          })

          // Create edge from metadata to first layer transactions
          if (transaction.layer === 1) {
            initialEdges.push({
              id: `metadata-${nodeId}`,
              source: 'metadata',
              target: nodeId,
              type: 'smoothstep',
              animated: true,
              style: { stroke: isDark ? '#00aaff' : '#6366f1' }
            })
          }
        })

        // Create edges between transaction layers
        complaintData.transactions.forEach((transaction, index) => {
          if (transaction.layer > 1) {
            // Find parent transaction (previous layer with same sender)
            const parentTransaction = complaintData.transactions.find((t, i) =>
              i < index &&
              t.layer === transaction.layer - 1 &&
              t.receiver_account === transaction.sender_account
            )

            if (parentTransaction) {
              const parentIndex = complaintData.transactions.indexOf(parentTransaction)
              initialEdges.push({
                id: `transaction-${parentIndex}-transaction-${index}`,
                source: `transaction-${parentIndex}`,
                target: `transaction-${index}`,
                type: 'smoothstep',
                animated: false,
                style: { stroke: isDark ? '#10b981' : '#059669' },
                label: `₹${transaction.amount}`
              })
            }
          }
        })
      }

      setNodes(initialNodes)
      setEdges(initialEdges)

    } catch (error) {
      showError('Failed to process graph data: ' + (error instanceof Error ? error.message : String(error)))
    }
  }, [complaintData, isDark, setNodes, setEdges, showError])

  return (
    <div className="w-full h-full">
      <ReactFlow
        nodes={nodes}
        edges={edges}
        nodeTypes={nodeTypes}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        fitView
        fitViewOptions={{
          padding: 0.2,
          minZoom: 0.1,
          maxZoom: 1.5
        }}
        nodesDraggable={true}
        nodesConnectable={true}
        elementsSelectable={true}
        panOnDrag={true}
        zoomOnScroll={true}
        zoomOnPinch={true}
        deleteKeyCode="Delete"
        className={isDark ? 'dark' : 'light'}
        style={{
          background: isDark ? '#1a1a1a' : '#f8f9fa',
          width: '100%',
          height: '100%'
        }}
      >
        <Background
          color={isDark ? '#333' : '#ddd'}
          gap={16}
          size={1}
        />
        <Controls />
        <MiniMap
          nodeColor={isDark ? '#374151' : '#e5e7eb'}
          maskColor={isDark ? 'rgba(0, 0, 0, 0.8)' : 'rgba(255, 255, 255, 0.8)'}
        />

        {/* Export Panel */}
        <Panel position="top-right">
          <div className="flex gap-2">
            <button
              onClick={() => {
                // Simple image download using html-to-image
                import('html-to-image').then(({ toPng }) => {
                  const element = document.querySelector('.react-flow') as HTMLElement
                  if (element) {
                    toPng(element)
                      .then((dataUrl) => {
                        const link = document.createElement('a')
                        link.download = `graph-${complaintId}.png`
                        link.href = dataUrl
                        link.click()
                      })
                      .catch((error) => {
                        showError('Failed to export image: ' + error.message)
                      })
                  }
                })
              }}
              className={`px-3 py-2 rounded text-sm font-medium ${
                isDark
                  ? 'bg-blue-600 hover:bg-blue-700 text-white'
                  : 'bg-blue-500 hover:bg-blue-600 text-white'
              }`}
            >
              📷 Export Image
            </button>
          </div>
        </Panel>
      </ReactFlow>
    </div>
  )
}
