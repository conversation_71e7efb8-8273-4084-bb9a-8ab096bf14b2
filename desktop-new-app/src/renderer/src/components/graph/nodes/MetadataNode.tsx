import React, { useState } from 'react'
import { NodeProps, <PERSON>le, Position, useReactFlow } from '@xyflow/react'
import { useTheme } from '../../../context/useThemeContext'

export const MetadataNode: React.FC<NodeProps> = ({ data, id }) => {
  const { isDark } = useTheme()
  const [isExpanded, setIsExpanded] = useState(true)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const { setNodes } = useReactFlow()

  // Respect global expansion state if provided
  React.useEffect(() => {
    if (data.forceExpanded !== undefined) {
      setIsExpanded(data.forceExpanded)
    }
  }, [data.forceExpanded])

  // Metadata node styling - distinct from transaction nodes
  const nodeStyle = {
    bg: isDark ? 'bg-indigo-900/30' : 'bg-indigo-50',
    border: isDark ? 'border-indigo-700' : 'border-indigo-300',
    text: isDark ? 'text-indigo-400' : 'text-indigo-700'
  }

  // Function to delete this node
  const handleDeleteNode = () => {
    setNodes((nodes) => nodes.filter((node) => node.id !== id))
    setShowDeleteConfirm(false)
  }

  // Function to toggle expanded/collapsed state
  const toggleExpanded = (e: React.MouseEvent) => {
    e.stopPropagation()
    setIsExpanded(!isExpanded)
  }

  return (
    <div
      className={`p-3 rounded-lg border shadow-lg w-[350px] ${nodeStyle.bg} ${nodeStyle.border} transition-all duration-200`}
      style={{ cursor: 'move' }}
    >
      {/* Add source handle only for metadata nodes */}
      <Handle
        type="source"
        position={Position.Bottom}
        style={{ background: isDark ? '#6366f1' : '#3b82f6', width: '10px', height: '10px' }}
      />

      {/* Header with controls */}
      <div className="flex justify-between items-center mb-2">
        <div
          className={`font-bold text-lg pb-1 border-b-2 border-indigo-200 dark:border-indigo-800 ${nodeStyle.text} flex-grow cursor-pointer`}
          onClick={toggleExpanded}
          title={isExpanded ? "Click to collapse" : "Click to expand"}
        >
          📋 Complaint Metadata
          <span className="ml-2 text-sm">
            {isExpanded ? (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            ) : (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            )}
          </span>
        </div>
        <div className="flex gap-1 ml-2">
          <button
            onClick={() => setShowDeleteConfirm(true)}
            className={`p-0.5 rounded ${isDark ? 'hover:bg-red-900/50 text-red-400' : 'hover:bg-red-100 text-red-600'}`}
            title="Delete node"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>

      {/* Delete confirmation dialog */}
      {showDeleteConfirm && (
        <div className={`absolute top-0 left-0 w-full h-full ${nodeStyle.bg} rounded-lg border ${nodeStyle.border} z-10 flex flex-col justify-center items-center p-3`}>
          <p className={`text-center mb-3 ${isDark ? 'text-white' : 'text-gray-800'}`}>Delete this metadata node?</p>
          <div className="flex gap-2">
            <button
              onClick={handleDeleteNode}
              className={`px-3 py-1 rounded ${isDark ? 'bg-red-700 text-white' : 'bg-red-500 text-white'}`}
            >
              Yes
            </button>
            <button
              onClick={() => setShowDeleteConfirm(false)}
              className={`px-3 py-1 rounded ${isDark ? 'bg-gray-700 text-white' : 'bg-gray-200 text-gray-800'}`}
            >
              No
            </button>
          </div>
        </div>
      )}

      {/* Always show essential information */}
      <div className="flex flex-col gap-2 text-sm">
        {/* Complaint Number - Always visible */}
        {data.complaint_number && (
          <div className="flex justify-between items-center">
            <span className={`${isDark ? 'text-gray-400' : 'text-gray-500'} font-medium`}>Complaint #:</span>
            <span className={`font-bold ${isDark ? 'text-white' : 'text-gray-800'}`}>{data.complaint_number}</span>
          </div>
        )}

        {/* Complainant Name - Always visible */}
        {data.complainant_name && (
          <div className="flex justify-between items-center">
            <span className={`${isDark ? 'text-gray-400' : 'text-gray-500'} font-medium`}>Complainant:</span>
            <span className={`font-medium ${isDark ? 'text-white' : 'text-gray-800'}`}>{data.complainant_name}</span>
          </div>
        )}

        {/* Total Amount - Always visible */}
        {data.total_amount && (
          <div className="flex justify-between items-center">
            <span className={`${isDark ? 'text-gray-400' : 'text-gray-500'} font-medium`}>Total Amount:</span>
            <span className={`font-bold text-lg ${isDark ? 'text-red-400' : 'text-red-600'}`}>₹{data.total_amount}</span>
          </div>
        )}

        {/* Additional details - only show if expanded */}
        {isExpanded && (
          <>
            {/* Date of Complaint */}
            {data.date_of_complaint && (
              <div className="flex justify-between items-center">
                <span className={`${isDark ? 'text-gray-400' : 'text-gray-500'} font-medium`}>Date:</span>
                <span className={`${isDark ? 'text-white' : 'text-gray-800'}`}>{data.date_of_complaint}</span>
              </div>
            )}

            {/* Police Station */}
            {data.police_station && (
              <div className="flex justify-between items-center">
                <span className={`${isDark ? 'text-gray-400' : 'text-gray-500'} font-medium`}>Police Station:</span>
                <span className={`${isDark ? 'text-white' : 'text-gray-800'}`}>{data.police_station}</span>
              </div>
            )}

            {/* FIR Number */}
            {data.fir_number && (
              <div className="flex justify-between items-center">
                <span className={`${isDark ? 'text-gray-400' : 'text-gray-500'} font-medium`}>FIR #:</span>
                <span className={`${isDark ? 'text-white' : 'text-gray-800'}`}>{data.fir_number}</span>
              </div>
            )}

            {/* Fraud Type */}
            {data.fraud_type && (
              <div className="flex justify-between items-center">
                <span className={`${isDark ? 'text-gray-400' : 'text-gray-500'} font-medium`}>Fraud Type:</span>
                <span className={`${isDark ? 'text-white' : 'text-gray-800'}`}>{data.fraud_type}</span>
              </div>
            )}

            {/* Bank Details */}
            {data.bank_name && (
              <div className="flex justify-between items-center">
                <span className={`${isDark ? 'text-gray-400' : 'text-gray-500'} font-medium`}>Bank:</span>
                <span className={`${isDark ? 'text-white' : 'text-gray-800'}`}>{data.bank_name}</span>
              </div>
            )}

            {/* Account Number */}
            {data.account_number && (
              <div className="flex justify-between items-center">
                <span className={`${isDark ? 'text-gray-400' : 'text-gray-500'} font-medium`}>Account:</span>
                <span className={`${isDark ? 'text-white' : 'text-gray-800'} font-mono text-xs`}>{data.account_number}</span>
              </div>
            )}

            {/* Transaction Count */}
            {data.transaction_count && (
              <div className="flex justify-between items-center">
                <span className={`${isDark ? 'text-gray-400' : 'text-gray-500'} font-medium`}>Transactions:</span>
                <span className={`${isDark ? 'text-white' : 'text-gray-800'} font-bold`}>{data.transaction_count}</span>
              </div>
            )}

            {/* Max Layer */}
            {data.max_layer && (
              <div className="flex justify-between items-center">
                <span className={`${isDark ? 'text-gray-400' : 'text-gray-500'} font-medium`}>Max Layer:</span>
                <span className={`${isDark ? 'text-white' : 'text-gray-800'} font-bold`}>{data.max_layer}</span>
              </div>
            )}

            {/* Status */}
            {data.status && (
              <div className="flex justify-between items-center">
                <span className={`${isDark ? 'text-gray-400' : 'text-gray-500'} font-medium`}>Status:</span>
                <span className={`px-2 py-1 rounded text-xs font-medium ${
                  data.status === 'Active' 
                    ? (isDark ? 'bg-green-900/50 text-green-400' : 'bg-green-100 text-green-700')
                    : (isDark ? 'bg-yellow-900/50 text-yellow-400' : 'bg-yellow-100 text-yellow-700')
                }`}>
                  {data.status}
                </span>
              </div>
            )}

            {/* Additional metadata fields */}
            {data.description && (
              <div className="mt-2 pt-2 border-t border-gray-200 dark:border-gray-700">
                <span className={`${isDark ? 'text-gray-400' : 'text-gray-500'} font-medium block mb-1`}>Description:</span>
                <span className={`${isDark ? 'text-white' : 'text-gray-800'} text-xs leading-relaxed`}>
                  {data.description}
                </span>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  )
}
