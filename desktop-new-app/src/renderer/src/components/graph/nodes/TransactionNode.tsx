import React, { ReactNode, useState } from 'react'
import { NodeProps, Handle, Position } from '@xyflow/react'
import { useThemeContext } from '../../../context/useThemeContext'

export const TransactionNode: React.FC<NodeProps> = ({ data }) => {
  const { isDark } = useThemeContext()
  const [isExpanded, setIsExpanded] = useState(true)

  // Determine node type - sender, main, sub, or special
  const isSenderNode = data.isSenderNode === true
  const isMainTransaction = data.isMainTransaction === true
  const isSubTransaction = data.isSubTransaction === true
  const isSpecialTxn = data.isSpecial === true

  // Simple styling based on transaction type
  const getNodeStyle = () => {
    // Sender node - blue
    if (isSenderNode) {
      return {
        bg: isDark ? 'bg-blue-900/30' : 'bg-blue-50',
        border: isDark ? 'border-blue-700' : 'border-blue-300',
        text: isDark ? 'text-blue-400' : 'text-blue-700'
      }
    }

    // Special transaction - red
    if (isSpecialTxn) {
      return {
        bg: isDark ? 'bg-red-900/30' : 'bg-red-50',
        border: isDark ? 'border-red-700' : 'border-red-300',
        text: isDark ? 'text-red-400' : 'text-red-700'
      }
    }

    // Sub-transaction - purple
    if (isSubTransaction) {
      return {
        bg: isDark ? 'bg-purple-900/30' : 'bg-purple-50',
        border: isDark ? 'border-purple-700' : 'border-purple-300',
        text: isDark ? 'text-purple-400' : 'text-purple-700'
      }
    }

    // Main transaction - green
    if (isMainTransaction || data.type === 'Money Transfer to') {
      return {
        bg: isDark ? 'bg-green-900/30' : 'bg-green-50',
        border: isDark ? 'border-green-700' : 'border-green-300',
        text: isDark ? 'text-green-400' : 'text-green-700'
      }
    }

    // Default - amber
    return {
      bg: isDark ? 'bg-amber-900/30' : 'bg-amber-50',
      border: isDark ? 'border-amber-700' : 'border-amber-300',
      text: isDark ? 'text-amber-400' : 'text-amber-700'
    }
  }

  const nodeStyle = getNodeStyle()

  // Function to toggle expanded/collapsed state
  const toggleExpanded = (e: React.MouseEvent) => {
    e.stopPropagation()
    setIsExpanded(!isExpanded)
  }

  return (
    <div
      className={`p-3 rounded-lg border shadow-md w-[280px] ${nodeStyle.bg} ${nodeStyle.border} transition-all duration-200`}
    >
      {/* ReactFlow Handles */}
      <Handle
        type="target"
        position={Position.Top}
        style={{ background: isDark ? '#6366f1' : '#3b82f6', width: '8px', height: '8px' }}
      />
      <Handle
        type="source"
        position={Position.Bottom}
        style={{ background: isDark ? '#6366f1' : '#3b82f6', width: '8px', height: '8px' }}
      />

      {/* Header */}
      <div className="flex justify-between items-center mb-2">
        <div
          className={`font-medium text-base pb-1 border-b border-gray-200 dark:border-gray-700 ${nodeStyle.text} flex-grow cursor-pointer`}
          onClick={toggleExpanded}
          title={isExpanded ? "Click to collapse" : "Click to expand"}
        >
          {data.label || 'Transaction'}
          <span className="ml-2 text-xs">
            {isExpanded ? (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            ) : (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            )}
          </span>
        </div>
      </div>

      {/* Transaction Details */}
      <div className="flex flex-col gap-1 text-sm">
        {/* Account - Always visible */}
        {data.account && (
          <div className="flex justify-between items-start">
            <span className={`${isDark ? 'text-gray-400' : 'text-gray-500'} font-medium flex-shrink-0 mr-2`}>Account:</span>
            <span className={`${isDark ? 'text-white' : 'text-gray-800'} text-right break-words text-xs leading-tight max-w-[160px]`}>
              {data.account}
            </span>
          </div>
        )}

        {/* Amount - Always visible */}
        {data.amount && (
          <div className="flex justify-between">
            <span className={`${isDark ? 'text-gray-400' : 'text-gray-500'} font-medium`}>Amount:</span>
            <span className={`font-medium ${isDark ? 'text-white' : 'text-gray-800'}`}>₹{data.amount}</span>
          </div>
        )}

        {/* Additional details - only show if expanded */}
        {isExpanded && (
          <>
            {data.date && (
              <div className="flex justify-between">
                <span className={`${isDark ? 'text-gray-400' : 'text-gray-500'} font-medium`}>Date:</span>
                <span className={`${isDark ? 'text-white' : 'text-gray-800'}`}>{data.date}</span>
              </div>
            )}

            {data.type && (
              <div className="flex justify-between">
                <span className={`${isDark ? 'text-gray-400' : 'text-gray-500'} font-medium`}>Type:</span>
                <span className={`${isDark ? 'text-white' : 'text-gray-800'}`}>{data.type}</span>
              </div>
            )}

            {data.txn_id && (
              <div className="flex justify-between items-start">
                <span className={`${isDark ? 'text-gray-400' : 'text-gray-500'} font-medium flex-shrink-0 mr-2`}>Txn ID:</span>
                <span className={`${isDark ? 'text-white' : 'text-gray-800'} font-mono text-xs text-right break-all leading-tight max-w-[160px]`}>
                  {data.txn_id}
                </span>
              </div>
            )}

            {data.sender_account && !data.isSenderNode && (
              <div className="flex justify-between items-start">
                <span className={`${isDark ? 'text-gray-400' : 'text-gray-500'} font-medium flex-shrink-0 mr-2`}>From:</span>
                <span className={`${isDark ? 'text-white' : 'text-gray-800'} text-right break-words text-xs leading-tight max-w-[160px]`}>
                  {data.sender_account}
                </span>
              </div>
            )}

            {data.receiver_info && (
              <div className="flex justify-between items-start">
                <span className={`${isDark ? 'text-gray-400' : 'text-gray-500'} font-medium flex-shrink-0 mr-2`}>Reference:</span>
                <span className={`${isDark ? 'text-white' : 'text-gray-800'} text-xs text-right break-words leading-tight max-w-[160px]`}>
                  {data.receiver_info}
                </span>
              </div>
            )}

            {data.layer && (
              <div className="flex justify-between">
                <span className={`${isDark ? 'text-gray-400' : 'text-gray-500'} font-medium`}>Layer:</span>
                <span className={`${isDark ? 'text-white' : 'text-gray-800'}`}>{data.layer}</span>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  )
}
