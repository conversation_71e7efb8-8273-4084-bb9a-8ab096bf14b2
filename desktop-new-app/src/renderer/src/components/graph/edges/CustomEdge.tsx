import React from 'react'
import { EdgeProps, getSmooth<PERSON>tep<PERSON>ath, EdgeLabelRenderer, BaseEdge, useReactFlow } from '@xyflow/react'
import { useTheme } from '../../../context/useThemeContext'

export const CustomEdge: React.FC<EdgeProps> = ({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  style = {},
  data,
  markerEnd
}) => {
  const { isDark } = useTheme()
  const { setEdges } = useReactFlow()

  // Calculate the path for the edge
  const [edgePath, labelX, labelY] = getSmoothStepPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
    borderRadius: 10
  })

  // Determine edge styling based on data or default
  const getEdgeStyle = () => {
    const baseStyle = {
      strokeWidth: 2,
      stroke: isDark ? '#00aaff' : '#6366f1',
      ...style
    }

    // Special styling for different edge types
    if (data?.type === 'money_flow') {
      return {
        ...baseStyle,
        stroke: isDark ? '#10b981' : '#059669', // Green for money flow
        strokeWidth: 3,
        strokeDasharray: data.isSpecial ? '5,5' : 'none'
      }
    }

    if (data?.type === 'parent_child') {
      return {
        ...baseStyle,
        stroke: isDark ? '#8b5cf6' : '#7c3aed', // Purple for parent-child relationships
        strokeWidth: 2,
        strokeDasharray: '3,3'
      }
    }

    if (data?.isHighValue) {
      return {
        ...baseStyle,
        stroke: isDark ? '#ef4444' : '#dc2626', // Red for high-value transactions
        strokeWidth: 4
      }
    }

    return baseStyle
  }

  const edgeStyle = getEdgeStyle()

  // Handle edge deletion
  const handleDeleteEdge = () => {
    setEdges((edges) => edges.filter((edge) => edge.id !== id))
  }

  // Determine if edge should be animated
  const isAnimated = data?.animated || data?.type === 'money_flow'

  return (
    <>
      <BaseEdge
        path={edgePath}
        markerEnd={markerEnd}
        style={{
          ...edgeStyle,
          animation: isAnimated ? 'dash 2s linear infinite' : 'none'
        }}
      />
      
      {/* Edge label with controls */}
      <EdgeLabelRenderer>
        <div
          style={{
            position: 'absolute',
            transform: `translate(-50%, -50%) translate(${labelX}px,${labelY}px)`,
            fontSize: 12,
            pointerEvents: 'all'
          }}
          className="nodrag nopan"
        >
          {/* Edge label content */}
          {(data?.label || data?.amount) && (
            <div
              className={`px-2 py-1 rounded shadow-md border text-xs font-medium ${
                isDark 
                  ? 'bg-gray-800 border-gray-600 text-white' 
                  : 'bg-white border-gray-300 text-gray-800'
              }`}
            >
              {/* Amount label */}
              {data?.amount && (
                <div className={`font-bold ${
                  data?.isHighValue 
                    ? (isDark ? 'text-red-400' : 'text-red-600')
                    : (isDark ? 'text-green-400' : 'text-green-600')
                }`}>
                  ₹{data.amount}
                </div>
              )}
              
              {/* Transaction type or custom label */}
              {data?.label && (
                <div className={isDark ? 'text-gray-300' : 'text-gray-600'}>
                  {data.label}
                </div>
              )}
              
              {/* Transaction date */}
              {data?.date && (
                <div className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                  {data.date}
                </div>
              )}
              
              {/* Transaction ID */}
              {data?.txn_id && (
                <div className={`text-xs font-mono ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                  ID: {data.txn_id.substring(0, 8)}...
                </div>
              )}
            </div>
          )}
          
          {/* Edge controls - only show on hover */}
          <div className="flex gap-1 mt-1 opacity-0 hover:opacity-100 transition-opacity">
            {/* Delete edge button */}
            <button
              onClick={handleDeleteEdge}
              className={`p-1 rounded text-xs ${
                isDark 
                  ? 'bg-red-900/50 text-red-400 hover:bg-red-800/70' 
                  : 'bg-red-100 text-red-600 hover:bg-red-200'
              }`}
              title="Delete edge"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
            
            {/* Edit edge button */}
            <button
              onClick={() => {
                // TODO: Implement edge editing functionality
                console.log('Edit edge:', id)
              }}
              className={`p-1 rounded text-xs ${
                isDark 
                  ? 'bg-blue-900/50 text-blue-400 hover:bg-blue-800/70' 
                  : 'bg-blue-100 text-blue-600 hover:bg-blue-200'
              }`}
              title="Edit edge"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
            </button>
          </div>
        </div>
      </EdgeLabelRenderer>
      
      {/* CSS for edge animation */}
      <style jsx>{`
        @keyframes dash {
          to {
            stroke-dashoffset: -10;
          }
        }
      `}</style>
    </>
  )
}
